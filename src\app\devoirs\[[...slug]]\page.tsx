import { ContentBrowser } from '@/components/content-browser';
import { siteData } from '@/lib/data';
import type { Metadata } from 'next';

type Props = {
  params: { slug?: string[] };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  let title = siteData.sections.devoirs.name;
  return {
    title: ` ${title} | التعليم المغربي`,
  };
}

export default function DevoirsPage({ params }: Props) {
  return <ContentBrowser section="devoirs" slug={params.slug || []} />;
}
