
import { getNewsArticles } from '@/lib/data';
import { Card, CardContent } from '@/components/ui/card';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { CalendarDays, ChevronLeft, Newspaper } from 'lucide-react';
import { Breadcrumbs } from '@/components/breadcrumbs';

export const metadata = {
  title: 'الأخبار | التعليم المغربي',
  description: 'آخر الأخبار والمستجدات في قطاع التعليم المغربي.',
};

export default async function NewsPage() {
  const news = await getNewsArticles();
  const breadcrumbs = [
    { name: 'الرئيسية', url: '/' },
    { name: 'الأخبار', url: '/news' }
  ];

  return (
    <>
      <Breadcrumbs items={breadcrumbs} />
      <div className="container mx-auto px-4 py-8 fade-in">
        <div className="flex items-center gap-4 mb-8">
          <Newspaper className="h-10 w-10 text-primary" />
          <div>
            <h1 className="text-3xl font-bold">الأخبار التعليمية</h1>
            <p className="text-gray-600">
              آخر الأخبار والمستجدات في التعليم المغربي
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {news.map((article: any) => (
            <Link href={`/news/${article.slug}`} key={article.id}>
              <Card className="h-full hover-scale group cursor-pointer">
                <CardContent className="p-6">
                  <div className="text-4xl mb-4 text-center">
                    {article.image}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mb-3">
                    <CalendarDays className="h-4 w-4" />
                    <span>{article.date}</span>
                  </div>
                  <h3 className="text-lg font-semibold mb-3 group-hover:text-primary transition-colors">
                    {article.title}
                  </h3>
                  <p className="text-muted-foreground text-sm mb-4">
                    {article.excerpt}
                  </p>
                  <Button variant="link" className="p-0 text-primary">
                      اقرأ المزيد <ChevronLeft className="h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </>
  );
}
