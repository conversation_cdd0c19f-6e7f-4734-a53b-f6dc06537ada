
import {
  BookOpen,
  CalendarDays,
  ChevronLeft,
  GraduationCap,
  Library,
  Backpack,
  Goal,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { siteData, getNewsArticles } from '@/lib/data';
import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'موقع التعليم المغربي - دروس، فروض، امتحانات مجانية',
  description: 'موقع تعليمي مغربي شامل - دروس مجانية، فروض، امتحانات، وثائق للابتدائي والإعدادي والثانوي. أرشيف كامل للتعليم المغربي',
  keywords: 'دروس مغربية، امتحان وطني 2 باك، فروض الثانوي، التعليم المغربي، دروس مجانية، امتحانات pdf',
  openGraph: {
    title: 'موقع التعليم المغربي',
    description: 'أرشيف شامل ومجاني للدروس، الفروض، الامتحانات والوثائق التعليمية',
    type: 'website',
    locale: 'ar_MA',
  },
};

const levelData = [
  {
    key: 'primary',
    icon: <Backpack className="h-12 w-12 mx-auto text-primary" />,
    title: 'التعليم الابتدائي',
    description: 'من السنة الأولى إلى السادسة ابتدائي',
    levels: ['1 ابتدائي', '2 ابتدائي', '3 ابتدائي', '+3 أخرى'],
    stats: '📊 450+ درس • 200+ فرض',
    color: 'blue',
  },
  {
    key: 'college',
    icon: <GraduationCap className="h-12 w-12 mx-auto text-destructive" />,
    title: 'التعليم الإعدادي',
    description: 'من السنة الأولى إلى الثالثة إعدادي',
    levels: ['1 إعدادي', '2 إعدادي', '3 إعدادي'],
    stats: '📊 800+ درس • 400+ فرض',
    color: 'red',
  },
  {
    key: 'lycee',
    icon: <Library className="h-12 w-12 mx-auto text-accent" />,
    title: 'التعليم الثانوي',
    description: 'الجذع المشترك، الأولى والثانية بكالوريا',
    levels: ['جذع مشترك', '1 باك', '2 باك'],
    stats: '📊 1,250+ درس • 600+ امتحان',
    color: 'green',
  },
];

const featureData = [
  {
    icon: '🆓',
    title: 'مجاني بالكامل',
    description: 'جميع المحتويات متاحة مجاناً بدون أي رسوم أو اشتراكات',
  },
  {
    icon: '📱',
    title: 'متوافق مع الجوال',
    description: 'يعمل بشكل مثالي على جميع الأجهزة والشاشات',
  },
  {
    icon: '🔄',
    title: 'تحديث مستمر',
    description: 'إضافة محتوى جديد وتحديث المواد بانتظام',
  },
];

const popularContent = [
  {
    icon: '📊',
    title: 'امتحان وطني رياضيات',
    description: '2 باك علوم رياضية',
    downloads: '2,500',
    href: '/exams/lycee/2bac/2bac-science-math/math',
  },
  {
    icon: '⚛️',
    title: 'امتحان وطني فيزياء',
    description: '2 باك علوم فيزيائية',
    downloads: '2,100',
    href: '/exams/lycee/2bac/2bac-science-exp/physics',
  },
  {
    icon: '📝',
    title: 'دروس اللغة العربية',
    description: '3 إعدادي',
    downloads: '1,850',
    href: '/lessons/college/3ac/arabic',
  },
  {
    icon: '📋',
    title: 'فروض الرياضيات',
    description: '1 باك علوم',
    downloads: '1,650',
    href: '/devoirs/lycee/1bac/1bac-science-exp/math',
  },
];

export default async function Home() {
  const allNews = await getNewsArticles();
  const latestNews = allNews.slice(0, 3);

  return (
    <div className="fade-in">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <section className="bg-gradient-to-l from-primary to-blue-600 text-white rounded-2xl p-8 md:p-12 mb-12">
          <div className="max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              موقع التعليم المغربي
            </h1>
            <p className="text-xl mb-6 opacity-90">
              أرشيف شامل ومجاني للدروس، الفروض، الامتحانات والوثائق التعليمية
            </p>
            <div className="flex flex-wrap gap-4">
              <Button size="lg" asChild className="bg-white text-primary hover:bg-gray-100">
                <Link href="/lessons">
                  <BookOpen className="ms-2" />
                  تصفح الدروس
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild className="border-2 border-white text-white hover:bg-white hover:text-primary">
                <Link href="/exams">
                  📋 الامتحانات الوطنية
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Statistics */}
        <section className="mb-12" aria-labelledby="statistics-heading">
          <h2 id="statistics-heading" className="sr-only">إحصائيات الموقع</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-primary mb-2" aria-label="عدد الدروس المجانية">
                  2,500+
                </div>
                <div className="text-muted-foreground">درس مجاني</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-destructive mb-2" aria-label="عدد الفروض والامتحانات">
                  1,200+
                </div>
                <div className="text-muted-foreground">فرض وامتحان</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-accent mb-2" aria-label="عدد الوثائق التعليمية">500+</div>
                <div className="text-muted-foreground">وثيقة تعليمية</div>
              </CardContent>
            </Card>
            <Card className="text-center">
              <CardContent className="p-6">
                <div className="text-3xl font-bold text-yellow-600 mb-2" aria-label="عدد التلاميذ المستفيدين">
                  50,000+
                </div>
                <div className="text-muted-foreground">تلميذ مستفيد</div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Quick Access */}
        <section className="mb-12" aria-labelledby="quick-access-heading">
          <h2 id="quick-access-heading" className="text-3xl font-bold mb-8 text-center">
            الوصول السريع حسب المستوى
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {levelData.map((level) => (
              <Link
                key={level.key}
                href={`/lessons/${level.key}`}
                aria-label={`انتقل إلى ${level.title} - ${level.description}`}
              >
                <Card className="hover-scale cursor-pointer text-center h-full group focus-within:ring-2 focus-within:ring-primary">
                    <CardHeader>
                        <div aria-hidden="true">{level.icon}</div>
                        <CardTitle className={`text-xl font-bold mt-3 group-hover:text-primary transition-colors text-card-foreground`}>
                          {level.title}
                        </CardTitle>
                    </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground mb-4">
                      {level.description}
                    </p>
                    <div className="flex flex-wrap gap-2 justify-center" role="list" aria-label="المستويات المتاحة">
                      {level.levels.map((lvl) => (
                        <Badge key={lvl} variant="secondary" role="listitem">{lvl}</Badge>
                      ))}
                    </div>
                    <div className="mt-4 text-sm text-muted-foreground/80" aria-label="إحصائيات المحتوى">
                      {level.stats}
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </section>

        {/* Popular Content */}
        <section className="mb-12">
            <h2 className="text-3xl font-bold mb-8 text-center">المحتوى الأكثر طلباً</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                {popularContent.map((item, index) => (
                    <Link key={index} href={item.href}>
                        <Card className="hover-scale cursor-pointer group h-full">
                            <CardContent className="p-6">
                                <div className="text-4xl mb-4">{item.icon}</div>
                                <h4 className="font-semibold mb-1 group-hover:text-primary transition-colors">{item.title}</h4>
                                <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                                <div className="text-xs text-muted-foreground/80">📥 {item.downloads} تحميل</div>
                            </CardContent>
                        </Card>
                    </Link>
                ))}
            </div>
        </section>


        {/* Features Section */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold mb-8 text-center">مميزات الموقع</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {featureData.map((feature, index) => (
              <Card key={index} className="text-center">
                <CardContent className="p-8">
                  <div className="text-5xl mb-4">{feature.icon}</div>
                  <h3 className="text-lg font-semibold mb-3">{feature.title}</h3>
                  <p className="text-muted-foreground">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Latest News */}
        <section>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-3xl font-bold">آخر الأخبار التعليمية</h2>
            <Button variant="link" asChild className="text-primary">
              <Link href="/news">
                عرض الكل <ChevronLeft className="h-4 w-4" />
              </Link>
            </Button>
          </div>
          <div className="grid md:grid-cols-3 gap-6">
            {latestNews.map((news: any) => (
              <Link href={`/news/${news.slug}`} key={news.id}>
                <Card className="h-full hover-scale group">
                  <CardContent className="p-6">
                    <div className="text-sm text-muted-foreground mb-2 flex items-center gap-2">
                        <CalendarDays className="h-4 w-4" /> {news.date}
                    </div>
                    <h3 className="font-semibold mb-3 group-hover:text-primary transition-colors">{news.title}</h3>
                    <p className="text-muted-foreground text-sm mb-4">
                      {news.excerpt}
                    </p>
                    <Button variant="link" className="p-0 text-primary">
                      اقرأ المزيد <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
}
