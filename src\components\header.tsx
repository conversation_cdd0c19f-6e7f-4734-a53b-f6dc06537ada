"use client";

import Link from 'next/link';
import {
  BookOpen,
  Menu,
  Search as SearchIcon,
  Globe,
  ChevronDown,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTrigger,
} from '@/components/ui/sheet';
import { useState } from 'react';
import { siteData } from '@/lib/data';
import { useRouter } from 'next/navigation';

const mainNavLinks = [
    { href: '/', label: '🏠 الرئيسية' },
    { href: '/lessons', label: '📖 الدروس' },
    { href: '/devoirs', label: '📝 الفروض' },
    { href: '/exams', label: '📋 الامتحانات' },
    { href: '/docs', label: '📄 الوثائق' },
    { href: '/orientation', label: '🎯 التوجيه' },
    { href: '/concours', label: '🏆 المباريات' },
    { href: '/tests', label: '🧪 اختبارات تفاعلية' },
    { href: '/news', label: '📰 الأخبار' },
];

export function Header() {
  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);
  const router = useRouter();

  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const query = e.currentTarget.value;
      if (query) {
        // In a real app, you would navigate to a search results page
        // For now, we log it, and can build the search page later.
        console.log('Searching for:', query);
        // Example: router.push(`/search?q=${encodeURIComponent(query)}`);
      }
    }
  };

  return (
    <header className="bg-card shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4">
        {/* Top Bar */}
        <div className="flex justify-between items-center py-2 text-sm border-b">
          <div className="flex items-center gap-4">
            <span className="text-muted-foreground">🇲🇦 التعليم المغربي</span>
          </div>
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4 text-muted-foreground" />
            <select
              id="languageSelector"
              className="bg-transparent border-none text-sm text-foreground focus:outline-none"
            >
              <option value="ar">العربية</option>
              <option value="fr">Français</option>
            </select>
          </div>
        </div>

        {/* Main Header */}
        <div className="flex items-center justify-between py-4">
          <Link href="/" className="flex items-center gap-4">
            <BookOpen className="h-8 w-8 text-primary" />
            <div>
              <div className="text-xl font-bold text-primary font-headline">
                التعليم المغربي
              </div>
              <div className="hidden md:block text-xs text-muted-foreground">
                أرشيف شامل ومجاني
              </div>
            </div>
          </Link>

          {/* Search */}
          <div className="flex-1 max-w-lg mx-8 hidden md:block">
            <div className="relative">
              <Input
                type="text"
                id="searchInput"
                placeholder="ابحث عن درس، فرض، امتحان..."
                className="w-full pe-10"
                onKeyDown={handleSearch}
              />
              <SearchIcon className="absolute start-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Sheet open={isMobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[280px]">
                <SheetHeader>
                   <Link href="/" className="flex items-center gap-2 mb-4" onClick={() => setMobileMenuOpen(false)}>
                        <BookOpen className="h-6 w-6 text-primary" />
                        <span className="text-lg font-bold text-primary">التعليم المغربي</span>
                   </Link>
                </SheetHeader>
                <nav>
                  <ul className="space-y-2">
                    {mainNavLinks.map((link) => (
                      <li key={link.href}>
                        <Link
                          href={link.href}
                          className="block p-2 rounded-md hover:bg-muted"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {link.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </nav>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Desktop Navigation */}
        <nav className="border-t py-2 hidden md:block">
          <ul className="flex flex-wrap gap-x-6 gap-y-2 text-sm font-medium">
            {mainNavLinks.map((link) => (
              <li key={link.href}>
                <Link
                  href={link.href}
                  className="text-foreground hover:text-primary transition-colors pb-1"
                >
                  {link.label.split(" ")[1]}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </header>
  );
}
