import { siteData } from '@/lib/data';
import {
  BookOpen,
  Calculator,
  ChevronLeft,
  FileText,
  GraduationCap,
  Library,
  Backpack,
  PenSquare,
  Landmark,
  Globe,
  Atom,
  Leaf,
  BrainCircuit,
  Briefcase,
  Cpu,
  Palette,
  Goal,
  TestTube,
  BookMarked,
  Languages,
  Calendar,
} from 'lucide-react';
import { Breadcrumbs } from '@/components/breadcrumbs';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { notFound } from 'next/navigation';
import { Separator } from './ui/separator';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { cn } from '@/lib/utils';
import { getContentItem, getContentList } from '@/lib/data';

const icons = {
  BookOpen,
  Calculator,
  PenSquare,
  Landmark,
  Globe,
  Atom,
  Leaf,
  BrainCircuit,
  Briefcase,
  Cpu,
  Palette,
  Goal,
  TestTube,
  BookMarked,
  Languages,
  Backpack,
  GraduationCap,
  Library,
  FileText,
};

type IconName = keyof typeof icons;

function DynamicIcon({ name, className, ariaLabel }: {
  name: string;
  className?: string;
  ariaLabel?: string;
}) {
  const Icon = icons[name as IconName];
  if (!Icon) return <BookOpen className={cn('h-8 w-8', className)} aria-label={ariaLabel} />;
  return <Icon className={cn('h-8 w-8', className)} aria-label={ariaLabel} />;
}

// Helper to get nested data safely
function getNestedData(data: any, path: string[]) {
  return path.reduce((acc, key) => acc?.[key], data);
}

export async function ContentBrowser({
  section,
  slug,
}: {
  section: keyof typeof siteData.sections;
  slug: string[];
}) {
  const sectionInfo = siteData.sections[section];
  if (!sectionInfo) notFound();
  
  const breadcrumbs = [{ name: 'الرئيسية', url: '/' }];
  breadcrumbs.push({ name: sectionInfo.name, url: `/${section}` });

  let currentData: any = siteData.cycles;
  let path = `/${section}`;
  let currentBrowsePath = '';
  let subjectKey : keyof typeof siteData.subjects | null = null;
  
  for (let i = 0; i < slug.length; i++) {
    const segment = slug[i];
    let nextDataNode;

    if (i === 0) { // Cycle
      nextDataNode = currentData[segment];
    } else { // Level, Branch, Stream, or Subject
      const parentData = getNestedData(siteData.cycles, slug.slice(0, i));
      if (parentData?.levels?.[segment]) {
        nextDataNode = parentData.levels[segment];
      } else if (parentData?.branches?.[segment]) {
        nextDataNode = parentData.branches[segment];
      } else if (parentData?.streams?.[segment]) {
        nextDataNode = parentData.streams[segment];
      } else if (siteData.subjects[segment as keyof typeof siteData.subjects]) {
        subjectKey = segment as keyof typeof siteData.subjects;
        nextDataNode = null; // This is a subject, stop navigation
      } else {
        nextDataNode = null;
      }
    }

    if (nextDataNode) {
      currentData = nextDataNode;
      path += `/${segment}`;
      currentBrowsePath = currentBrowsePath ? `${currentBrowsePath}/${segment}` : segment;
      breadcrumbs.push({ name: currentData.name, url: path });
    } else {
      // It's a subject or a content slug, so we stop hierarchical navigation.
      if (subjectKey) {
        path += `/${subjectKey}`;
        breadcrumbs.push({ name: siteData.subjects[subjectKey].name, url: path });
      }
      break; 
    }
  }

  // Check if the final part of the slug is a content item
  let contentItem = null;
  if (slug.length > 0 && subjectKey) {
      const contentSlug = slug[slug.length-1];
      if (contentSlug !== subjectKey) {
        contentItem = await getContentItem(section, currentBrowsePath, contentSlug);
        if(contentItem) {
          breadcrumbs.pop(); // remove subject breadcrumb
          const subject = siteData.subjects[subjectKey as keyof typeof siteData.subjects];
          if(subject) breadcrumbs.push({ name: subject.name, url: path.substring(0, path.lastIndexOf('/')) });
          breadcrumbs.push({ name: contentItem.title, url: `${path}` });
        }
      }
  }
  
  const renderContent = async () => {
    if (contentItem) {
      return <ContentDetailView item={contentItem} section={section} />;
    }

    if (currentData.subjects) {
      return <SubjectsView data={currentData} basePath={path} />;
    }
    
    const isNavigationNode = currentData.levels || currentData.branches || currentData.streams;
    if (isNavigationNode && !subjectKey) {
       return <LevelsView data={currentData} basePath={path} />;
    }

    if (subjectKey) {
      const items = await getContentList(section, currentBrowsePath);
      const subjectInfo = siteData.subjects[subjectKey];
      return <ContentListView items={items} basePath={path} subject={subjectInfo} />;
    }

    // Default: Show cycles
    return <CyclesView basePath={`/${section}`} />;
  };

  return (
    <div>
      <Breadcrumbs items={breadcrumbs} />
      <div className="container mx-auto px-4 py-8">{await renderContent()}</div>
    </div>
  );
}


function CyclesView({ basePath }: { basePath: string }) {
    return (
        <div className="fade-in">
            <h1 className="text-3xl font-bold mb-8">اختر المستوى الدراسي</h1>
            <div className="grid md:grid-cols-3 gap-8" role="list">
                {Object.entries(siteData.cycles).map(([key, cycle]) => (
                    <Link
                        key={key}
                        href={`${basePath}/${key}`}
                        aria-label={`انتقل إلى ${cycle.name} - ${Object.keys(cycle.levels).length} مستويات دراسية`}
                        role="listitem"
                    >
                        <Card className="text-center hover-scale group cursor-pointer h-full focus-within:ring-2 focus-within:ring-primary">
                            <CardHeader>
                                <div className="mx-auto bg-primary/10 text-primary p-4 rounded-full w-fit">
                                   <DynamicIcon
                                     name={cycle.icon}
                                     className="h-10 w-10"
                                     ariaLabel={`أيقونة ${cycle.name}`}
                                   />
                                </div>
                                <CardTitle className="mt-4 group-hover:text-primary transition-colors">
                                    {cycle.name}
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-muted-foreground">
                                    {Object.keys(cycle.levels).length} مستويات دراسية
                                </p>
                            </CardContent>
                        </Card>
                    </Link>
                ))}
            </div>
        </div>
    );
}

function LevelsView({ data, basePath }: { data: any, basePath: string }) {
    const items = data.levels || data.branches || data.streams;
    const title = data.name;
    const subTitle = data.levels ? 'اختر المستوى' : 'اختر الشعبة أو المسلك';

    return (
        <div className="fade-in">
            <h1 className="text-3xl font-bold mb-2">{title}</h1>
            <p className="text-muted-foreground mb-8">{subTitle}</p>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Object.entries(items).map(([key, item]: [string, any]) => (
                    <Link key={key} href={`${basePath}/${key}`}>
                        <Card className="hover-scale cursor-pointer group h-full">
                           <CardContent className="p-6 text-center">
                             <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">{item.name}</h3>
                             {item.subjects && <p className="text-sm text-muted-foreground mt-2">{item.subjects.length} مادة</p>}
                             {item.streams && <p className="text-sm text-muted-foreground mt-2">{Object.keys(item.streams).length} مسالك</p>}
                             {item.branches && <p className="text-sm text-muted-foreground mt-2">{Object.keys(item.branches).length} شعب</p>}
                           </CardContent>
                        </Card>
                    </Link>
                ))}
            </div>
        </div>
    );
}


function SubjectsView({ data, basePath }: { data: any; basePath: string }) {
  return (
    <div className="fade-in">
      <h1 className="text-3xl font-bold mb-2">{data.name}</h1>
      <p className="text-muted-foreground mb-8">اختر المادة الدراسية</p>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {data.subjects.map((subjectKey: keyof typeof siteData.subjects) => {
          const subject = siteData.subjects[subjectKey];
          return (
            <Link key={subjectKey} href={`${basePath}/${subjectKey}`}>
              <Card className="text-center hover-scale group cursor-pointer h-full">
                <CardContent className="p-4 flex flex-col items-center justify-center">
                  <DynamicIcon name={subject.icon} className="h-8 w-8 mb-2 text-primary" />
                  <h3 className="font-semibold group-hover:text-primary transition-colors">
                    {subject.name}
                  </h3>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>
    </div>
  );
}

function ContentListView({ items, basePath, subject }: { items: any[], basePath: string, subject: any }) {
    return (
        <div className="fade-in">
             <h1 className="text-3xl font-bold mb-2">قائمة المحتوى: {subject.name}</h1>
             <p className="text-muted-foreground mb-8">تصفح المحتوى المتاح لهذه المادة.</p>
             {items.length > 0 ? (
                <div className="grid gap-6">
                    {items.map(item => (
                        <Link key={item.id} href={`${basePath}/${item.slug}`}>
                            <Card className="hover-scale cursor-pointer group">
                                <CardContent className="p-6">
                                    <div className="flex justify-between items-start">
                                      <div>
                                        <h3 className="text-xl font-semibold group-hover:text-primary transition-colors mb-2">{item.title}</h3>
                                        {item.description && <p className="text-muted-foreground mb-4 max-w-prose">{item.description}</p>}
                                      </div>
                                      <Button variant="ghost" className="hidden md:inline-flex">
                                        عرض <ChevronLeft className="h-4 w-4" />
                                      </Button>
                                    </div>
                                    <div className="flex items-center gap-4 text-sm text-muted-foreground pt-4 border-t mt-4">
                                        {item.downloads && <span>📥 {item.downloads.toLocaleString('ar-MA')} تحميل</span>}
                                        {item.year && <span>📅 {item.year}</span>}
                                        {item.session && <Badge variant="secondary">{item.session}</Badge>}
                                        {item.chapters && <span>📚 {item.chapters} فصول</span>}
                                    </div>
                                </CardContent>
                            </Card>
                        </Link>
                    ))}
                </div>
             ) : (
                <div className="text-center py-20">
                    <Card className="max-w-md mx-auto">
                        <CardContent className="p-10">
                            <BookOpen className="h-20 w-20 mx-auto text-muted-foreground/30 mb-4" />
                            <h2 className="text-2xl font-bold">لا يوجد محتوى حالياً</h2>
                            <p className="text-muted-foreground mt-2">نعمل على إضافة المزيد من المحتوى. يرجى التحقق مرة أخرى قريباً.</p>
                        </CardContent>
                    </Card>
                </div>
             )}
        </div>
    )
}

function ContentDetailView({ item, section }: { item: any, section: string }) {
  const pageContent = `${item.title}. ${item.description || ''} ${
    item.content || ''
  }`;

  return (
    <div className="fade-in">
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-start">
              <CardTitle className="text-3xl max-w-4xl">{item.title}</CardTitle>
              <Button size="lg">📥 تحميل الملف (PDF)</Button>
            </div>
            <div className="flex flex-wrap items-center gap-x-6 gap-y-2 text-muted-foreground pt-4">
              {item.downloads && (
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />{' '}
                  <span>{item.downloads.toLocaleString('ar-MA')} تحميل</span>
                </div>
              )}
              {item.year && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" /> <span>{item.year}</span>
                </div>
              )}
              {item.session && <Badge variant="default">{item.session}</Badge>}
              {item.chapters && (
                <div className="flex items-center gap-2">
                  <Library className="h-4 w-4" />{' '}
                  <span>{item.chapters} فصول</span>
                </div>
              )}
            </div>
          </CardHeader>
          <Separator />
          <CardContent className="pt-6">
            <article className="prose prose-lg max-w-none dark:prose-invert">
              {item.description && (
                <>
                  <h2>وصف المحتوى</h2>
                  <p>{item.description}</p>
                </>
              )}

              {item.content && (
                <>
                  <h2>المحتوى الكامل</h2>
                  <div dangerouslySetInnerHTML={{ __html: item.content }} />
                </>
              )}

              {!item.description && !item.content && (
                <p>لا يتوفر محتوى تفصيلي لهذا العنصر حاليًا.</p>
              )}

              {item.questions && (
                <>
                  <h2>أسئلة الامتحان</h2>
                  <Accordion type="single" collapsible className="w-full">
                    {item.questions.map(
                      (q: { question: string; answer: string }, i: number) => (
                        <AccordionItem value={`item-${i}`} key={i}>
                          <AccordionTrigger>{q.question}</AccordionTrigger>
                          <AccordionContent>
                            <p className="pb-4">{q.answer}</p>
                          </AccordionContent>
                        </AccordionItem>
                      )
                    )}
                  </Accordion>
                </>
              )}
            </article>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

    