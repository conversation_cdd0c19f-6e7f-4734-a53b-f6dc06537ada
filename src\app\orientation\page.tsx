import { Breadcrumbs } from '@/components/breadcrumbs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getCollection } from '@/lib/data';
import { GraduationCap, Briefcase, Trophy, FileText, Download } from 'lucide-react';
import Link from 'next/link';

export const metadata = {
  title: 'التوجيه | التعليم المغربي',
  description: 'دليلك للاختيار الأمثل لمسارك التعليمي والمهني بعد البكالوريا.',
};

const orientationSections = [
    {
        icon: <GraduationCap className="h-12 w-12 text-primary mx-auto" />,
        title: 'التوجيه الجامعي',
        description: 'معلومات عن الشعب والكليات الجامعية',
        stats: '50+ شعبة',
        color: 'blue'
    },
    {
        icon: <Briefcase className="h-12 w-12 text-accent mx-auto" />,
        title: 'التكوين المهني',
        description: 'دليل التكوين المهني والتقني',
        stats: '100+ تخصص',
        color: 'green'
    },
    {
        icon: <Trophy className="h-12 w-12 text-destructive mx-auto" />,
        title: 'المدارس العليا',
        description: 'مباريات ولوج المدارس العليا',
        stats: '30+ مدرسة',
        color: 'red'
    }
];

export default async function OrientationPage() {
  const breadcrumbs = [
    { name: 'الرئيسية', url: '/' },
    { name: 'التوجيه', url: '/orientation' },
  ];
  const orientationDocs = await getCollection('orientation');

  return (
    <>
      <Breadcrumbs items={breadcrumbs} />
      <div className="container mx-auto px-4 py-8 fade-in">
        <div className="text-center mb-12">
            <h1 className="text-4xl font-bold">التوجيه المدرسي والمهني</h1>
            <p className="text-lg text-muted-foreground mt-2">
            دليلك للاختيار الأمثل لمسارك التعليمي والمهني
            </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
            {orientationSections.map(section => (
                <Card key={section.title} className="text-center hover-scale cursor-pointer">
                    <CardHeader>
                        {section.icon}
                        <CardTitle className="mt-4">{section.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-muted-foreground mb-4">{section.description}</p>
                    </CardContent>
                </Card>
            ))}
        </div>

        <section>
            <h2 className="text-3xl font-bold mb-8 text-center">وثائق التوجيه</h2>
            <div className="grid gap-6">
                {orientationDocs.map((doc: any) => (
                    <Card key={doc.id} className="hover-scale">
                        <CardContent className="p-6">
                            <div className="flex flex-col md:flex-row justify-between md:items-center">
                                <div>
                                    <h3 className="text-xl font-semibold mb-2">{doc.title}</h3>
                                    <p className="text-muted-foreground mb-4">{doc.description}</p>
                                </div>
                                <Button asChild>
                                    <Link href="#">
                                        <Download className="ms-2 h-4 w-4"/>
                                        تحميل الدليل
                                    </Link>
                                </Button>
                            </div>
                            <div className="border-t mt-4 pt-4 flex items-center gap-4 text-sm text-muted-foreground">
                                <div className="flex items-center gap-1"><Download className="h-4 w-4"/> {doc.downloads} تحميل</div>
                                <div className="flex items-center gap-1"><FileText className="h-4 w-4"/> {doc.pages} صفحة</div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </section>

      </div>
    </>
  );
}
