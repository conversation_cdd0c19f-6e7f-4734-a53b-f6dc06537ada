import { Breadcrumbs } from '@/components/breadcrumbs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getCollection, siteData } from '@/lib/data';
import { FlaskConical, Atom, Calculator, PenSquare, Brain, Clock, HelpCircle, Users } from 'lucide-react';

export const metadata = {
  title: 'اختبارات تفاعلية | التعليم المغربي',
  description: 'اختبر معلوماتك وقيم مستواك في مختلف المواد مع اختبارات تفاعلية.',
};

const testCategories = [
    {
        icon: <Calculator className="h-12 w-12 text-primary mx-auto" />,
        title: "الرياضيات",
        description: "اختبارات في جميع فروع الرياضيات",
        count: 15
    },
    {
        icon: <Atom className="h-12 w-12 text-accent mx-auto" />,
        title: "الفيزياء والكيمياء",
        description: "اختبارات تفاعلية في العلوم الفيزيائية",
        count: 12
    },
    {
        icon: <PenSquare className="h-12 w-12 text-destructive mx-auto" />,
        title: "اللغة العربية",
        description: "اختبارات في النحو والأدب",
        count: 10
    }
];

export default async function TestsPage() {
  const breadcrumbs = [
    { name: 'الرئيسية', url: '/' },
    { name: 'اختبارات تفاعلية', url: '/tests' },
  ];
  const tests = await getCollection('tests');

  return (
    <>
      <Breadcrumbs items={breadcrumbs} />
      <div className="container mx-auto px-4 py-8 fade-in">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold">الاختبارات التفاعلية</h1>
          <p className="text-lg text-muted-foreground mt-2">
            اختبر معلوماتك وقيم مستواك في مختلف المواد
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
            {testCategories.map(cat => (
                 <Card key={cat.title} className="text-center hover-scale cursor-pointer">
                    <CardHeader>
                        {cat.icon}
                        <CardTitle className="mt-4">{cat.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p className="text-muted-foreground mb-4">{cat.description}</p>
                    </CardContent>
                 </Card>
            ))}
        </div>

        <section>
          <h2 className="text-3xl font-bold mb-8 text-center">الاختبارات المتاحة</h2>
          <div className="grid gap-6">
            {tests.map((test: any) => (
              <Card key={test.id}>
                <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row justify-between">
                        <div>
                            <h3 className="text-xl font-semibold mb-2">{test.title}</h3>
                            <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-muted-foreground text-sm mb-4">
                                <div className="flex items-center gap-1"><Brain className="h-4 w-4" /> {siteData.subjects[test.subject as keyof typeof siteData.subjects]?.name}</div>
                                <div className="flex items-center gap-1"><HelpCircle className="h-4 w-4" /> {test.questions} سؤال</div>
                                <div className="flex items-center gap-1"><Clock className="h-4 w-4" /> {test.duration} دقيقة</div>
                                <div className="flex items-center gap-1"><Users className="h-4 w-4" /> {test.attempts} محاولة</div>
                            </div>
                        </div>
                        <div className="flex-shrink-0 mt-4 md:mt-0">
                            <Button size="lg" className="bg-accent hover:bg-accent/90">
                                <FlaskConical className="ms-2 h-5 w-5"/>
                                بدء الاختبار
                            </Button>
                        </div>
                    </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      </div>
    </>
  );
}
