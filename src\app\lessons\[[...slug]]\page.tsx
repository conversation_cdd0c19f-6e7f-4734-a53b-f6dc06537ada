import { ContentBrowser } from '@/components/content-browser';
import { siteData } from '@/lib/data';
import type { Metadata } from 'next';

type Props = {
  params: { slug?: string[] };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const slug = params.slug || [];
  let title = siteData.sections.lessons.name;
  
  if (slug.length > 0) {
    // Basic implementation: just use the last slug segment
    // A real implementation would traverse data to find the real name
    const lastSegment = slug[slug.length-1].replace(/-/g, ' ');
    title = `${lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1)} | ${title}`;
  }

  return {
    title: ` ${title} | التعليم المغربي`,
  };
}

export default function LessonsPage({ params }: Props) {
  return <ContentBrowser section="lessons" slug={params.slug || []} />;
}
