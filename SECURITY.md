# سياسة الأمان - Security Policy

## الإصدارات المدعومة

نحن ندعم الإصدارات التالية بتحديثات الأمان:

| الإصدار | مدعوم          |
| ------- | ------------- |
| 1.0.x   | ✅ مدعوم      |
| < 1.0   | ❌ غير مدعوم  |

## الإبلاغ عن الثغرات الأمنية

إذا اكتشفت ثغرة أمنية، يرجى عدم الإبلاغ عنها علناً. بدلاً من ذلك:

1. أرسل بريد إلكتروني إلى: <EMAIL>
2. قدم وصفاً مفصلاً للثغرة
3. أرفق خطوات إعادة الإنتاج إن أمكن
4. سنرد خلال 48 ساعة

## الممارسات الأمنية المطبقة

### حماية البيانات
- جميع مفاتيح API محمية في متغيرات البيئة
- لا يتم تخزين بيانات حساسة في الكود المصدري
- استخدام HTTPS في جميع الاتصالات

### Firebase Security
- تطبيق قواعد Firestore Security Rules
- تشفير البيانات أثناء النقل والتخزين
- مراقبة الوصول والاستخدام

### Frontend Security
- تنظيف جميع المدخلات من المستخدمين
- حماية من XSS و CSRF
- استخدام Content Security Policy

### Dependencies
- فحص دوري للثغرات في التبعيات
- تحديث منتظم للمكتبات
- استخدام أدوات فحص الأمان

## التوصيات للمطورين

### متغيرات البيئة
```bash
# لا تضع أبداً مفاتيح API في الكود
# استخدم .env.local دائماً
NEXT_PUBLIC_FIREBASE_API_KEY=your_key_here
```

### Firebase Rules
```javascript
// مثال على قواعد Firestore آمنة
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### Content Security Policy
```javascript
// في next.config.ts
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: "default-src 'self'; script-src 'self' 'unsafe-eval';"
  }
];
```

## الاستجابة للحوادث

في حالة اكتشاف ثغرة أمنية:

1. **التقييم الفوري** (0-2 ساعة)
   - تحديد مستوى الخطورة
   - تقييم التأثير المحتمل

2. **الاحتواء** (2-24 ساعة)
   - إيقاف الخدمات المتأثرة إن لزم الأمر
   - تطبيق إصلاح مؤقت

3. **الإصلاح** (1-7 أيام)
   - تطوير إصلاح دائم
   - اختبار شامل للحل

4. **التواصل** (حسب الحاجة)
   - إشعار المستخدمين المتأثرين
   - نشر تحديث أمني

## الموارد الإضافية

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Next.js Security](https://nextjs.org/docs/advanced-features/security-headers)
- [Firebase Security](https://firebase.google.com/docs/rules)

---

**تذكر:** الأمان مسؤولية مشتركة. ساعدنا في الحفاظ على أمان المشروع.
