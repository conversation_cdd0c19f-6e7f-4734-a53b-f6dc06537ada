# **App Name**: MaghrebLearn

## Core Features:

- Dynamic Content Loading: Dynamic content loading based on user navigation, using the provided siteData and contentData.
- Breadcrumb Navigation: Breadcrumb navigation to show the user's current location within the site.
- Dynamic Search Results: Display search results dynamically, without database integration
- AI Material Suggester: AI-powered tool suggests related learning materials based on the current page content.
- Responsive Design: Mobile-responsive design using Tailwind CSS, ensuring the site is accessible on all devices.
- Analytics Integration: Integration of Google Analytics for traffic analysis.

## Style Guidelines:

- Primary color: HSL(221, 59%, 41%) - RGB(30, 64, 175). A saturated blue, suggesting trustworthiness and stability, suitable for an educational platform.
- Background color: HSL(210, 30%, 95%) - RGB(241, 245, 249). A light, desaturated shade of blue to provide a clean, non-distracting backdrop for content.
- Accent color: HSL(161, 90%, 30%) - RGB(5, 150, 105). A contrasting, vibrant green for calls to action and interactive elements, signifying growth and knowledge.
- Body and headline font: 'PT Sans', a humanist sans-serif font that balances modernity with a touch of warmth, ideal for both headlines and body text.
- Code font: 'Source Code Pro' for displaying code snippets.
- Consistent use of flat, outline-style icons throughout the interface, providing visual clarity without unnecessary ornamentation.
- Grid-based layout with generous whitespace to ensure readability and a professional aesthetic.
- Subtle fade-in effects on content loading and hover effects on interactive elements to enhance user experience without being intrusive.