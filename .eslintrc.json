{
  "extends": [
    "next/core-web-vitals",
    "next/typescript"
  ],
  "rules": {
    // تحسينات الأداء
    "@next/next/no-img-element": "error",
    "@next/next/no-page-custom-font": "warn",
    
    // تحسينات إمكانية الوصول
    "jsx-a11y/alt-text": "error",
    "jsx-a11y/aria-props": "error",
    "jsx-a11y/aria-proptypes": "error",
    "jsx-a11y/aria-unsupported-elements": "error",
    "jsx-a11y/role-has-required-aria-props": "error",
    "jsx-a11y/role-supports-aria-props": "error",
    
    // تحسينات TypeScript
    "@typescript-eslint/no-unused-vars": "warn",
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/prefer-const": "error",
    
    // تحسينات عامة
    "prefer-const": "error",
    "no-var": "error",
    "no-console": "warn"
  },
  "overrides": [
    {
      "files": ["*.ts", "*.tsx"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "off"
      }
    }
  ]
}
