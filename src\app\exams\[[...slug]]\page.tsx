import { ContentBrowser } from '@/components/content-browser';
import { siteData } from '@/lib/data';
import type { Metadata } from 'next';

type Props = {
  params: { slug?: string[] };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  let title = siteData.sections.exams.name;
  return {
    title: ` ${title} | التعليم المغربي`,
  };
}

export default function ExamsPage({ params }: Props) {
  return <ContentBrowser section="exams" slug={params.slug || []} />;
}
