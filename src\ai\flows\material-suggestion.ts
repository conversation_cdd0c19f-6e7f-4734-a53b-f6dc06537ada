'use server';
/**
 * @fileOverview Suggests relevant learning materials based on the current page content.
 *
 * - suggestRelevantLearningMaterials - A function that handles the suggestion process.
 * - MaterialSuggestionInput - The input type for the suggestRelevantLearningMaterials function.
 * - MaterialSuggestionOutput - The return type for the suggestRelevantLearningMaterials function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const MaterialSuggestionInputSchema = z.object({
  pageContent: z
    .string()
    .describe('The content of the current page the user is viewing.'),
});
export type MaterialSuggestionInput = z.infer<typeof MaterialSuggestionInputSchema>;

const MaterialSuggestionOutputSchema = z.object({
  suggestions: z
    .array(z.string())
    .describe('A list of suggested learning materials.'),
});
export type MaterialSuggestionOutput = z.infer<typeof MaterialSuggestionOutputSchema>;

export async function suggestRelevantLearningMaterials(input: MaterialSuggestionInput): Promise<MaterialSuggestionOutput> {
  return materialSuggestionFlow(input);
}

const prompt = ai.definePrompt({
  name: 'materialSuggestionPrompt',
  input: {schema: MaterialSuggestionInputSchema},
  output: {schema: MaterialSuggestionOutputSchema},
  prompt: `You are an AI assistant designed to suggest relevant learning materials based on the content of the current page.

  Please provide a list of relevant topics, URLs to external resources, or titles of learning materials (e.g. lessons, exams, videos) that would help the student deepen their understanding of the topic discussed in the page content below.

  Page Content: {{{pageContent}}}
  `,
});

const materialSuggestionFlow = ai.defineFlow(
  {
    name: 'materialSuggestionFlow',
    inputSchema: MaterialSuggestionInputSchema,
    outputSchema: MaterialSuggestionOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
