# موقع التعليم المغربي - MaghrebLearn

موقع تعليمي مغربي شامل مبني بتقنيات حديثة لتوفير دروس، فروض، امتحانات ووثائق تعليمية مجانية لجميع المستويات الدراسية.

## 🚀 التقنيات المستخدمة

- **Next.js 15** - إطار عمل React مع App Router
- **TypeScript** - للأمان النوعي
- **Tailwind CSS** - للتصميم المتجاوب
- **Firebase Firestore** - قاعدة البيانات
- **Radix UI** - مكونات واجهة المستخدم
- **Google AI (Genkit)** - للذكاء الاصطناعي
- **Lucide React** - الأيقونات

## 📋 المتطلبات

- Node.js 18+
- npm أو yarn أو pnpm
- حساب Firebase
- مفتاح Google AI API (اختياري)

## 🛠️ التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/Ridanotx/MaghrebLearn.git
cd MaghrebLearn
```

### 2. تثبيت التبعيات
```bash
npm install
# أو
yarn install
# أو
pnpm install
```

### 3. إعداد متغيرات البيئة
انسخ ملف `.env.example` إلى `.env.local`:
```bash
cp .env.example .env.local
```

املأ المتغيرات في `.env.local` بقيم Firebase الخاصة بك:
```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
GOOGLE_GENAI_API_KEY=your_google_ai_key
```

### 4. إعداد Firebase
1. أنشئ مشروع جديد في [Firebase Console](https://console.firebase.google.com)
2. فعّل Firestore Database
3. انسخ إعدادات المشروع إلى `.env.local`

### 5. تشغيل المشروع
```bash
npm run dev
# أو
yarn dev
# أو
pnpm dev
```

افتح [http://localhost:9002](http://localhost:9002) في المتصفح.

## 📊 ملء قاعدة البيانات

لملء قاعدة البيانات ببيانات تجريبية:
```bash
npm run db:seed
```

## 🏗️ بنية المشروع

```
src/
├── app/                 # صفحات Next.js (App Router)
│   ├── lessons/        # صفحات الدروس
│   ├── devoirs/        # صفحات الفروض
│   ├── exams/          # صفحات الامتحانات
│   └── ...
├── components/         # مكونات React
│   ├── ui/            # مكونات واجهة المستخدم
│   └── ...
├── lib/               # مكتبات ووظائف مساعدة
│   ├── data.ts        # وظائف قاعدة البيانات
│   ├── firebase.ts   # إعداد Firebase
│   └── utils.ts       # وظائف مساعدة
└── ai/                # إعدادات الذكاء الاصطناعي
```

## 🎯 الميزات

- ✅ دعم كامل للغة العربية (RTL)
- ✅ تصميم متجاوب لجميع الأجهزة
- ✅ إمكانية الوصول (Accessibility)
- ✅ تحسين محركات البحث (SEO)
- ✅ تحميل سريع وأداء محسن
- ✅ نظام تنقل هرمي للمحتوى
- ✅ بحث في المحتوى
- ✅ إدارة الأخبار التعليمية

## 🔧 الأوامر المتاحة

```bash
npm run dev          # تشغيل الخادم المحلي
npm run build        # بناء المشروع للإنتاج
npm run start        # تشغيل المشروع المبني
npm run lint         # فحص الكود
npm run typecheck    # فحص TypeScript
npm run db:seed      # ملء قاعدة البيانات
npm run genkit:dev   # تشغيل Genkit للذكاء الاصطناعي
```

## 🚀 النشر

### Vercel (موصى به)
1. ادفع الكود إلى GitHub
2. اربط المستودع بـ Vercel
3. أضف متغيرات البيئة في إعدادات Vercel
4. انشر المشروع

### Firebase Hosting
```bash
npm run build
firebase deploy
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## 📞 التواصل

- GitHub: [@Ridanotx](https://github.com/Ridanotx)
- Email: <EMAIL>

---

**ملاحظة:** هذا المشروع في مرحلة التطوير. بعض الميزات قد تكون غير مكتملة.
