"use client";

import Link from 'next/link';
import { ChevronLeft } from 'lucide-react';

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
}

export function Breadcrumbs({ items }: BreadcrumbsProps) {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div className="bg-muted/50 py-3 mb-8">
      <div className="container mx-auto px-4">
        <nav className="text-sm flex items-center flex-wrap" aria-label="Breadcrumb">
          {items.map((item, index) => (
            <div key={item.url} className="flex items-center">
              {index < items.length - 1 ? (
                <Link
                  href={item.url}
                  className="text-muted-foreground hover:text-primary transition-colors"
                >
                  {item.name}
                </Link>
              ) : (
                <span className="text-foreground font-medium" aria-current="page">
                  {item.name}
                </span>
              )}
              {index < items.length - 1 && (
                <ChevronLeft className="h-4 w-4 text-gray-400 mx-1" />
              )}
            </div>
          ))}
        </nav>
      </div>
    </div>
  );
}
