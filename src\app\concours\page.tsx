import { Breadcrumbs } from '@/components/breadcrumbs';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { getCollection } from '@/lib/data';
import { Building, Calendar, ChevronLeft, School } from 'lucide-react';
import Link from 'next/link';

export const metadata = {
  title: 'المباريات | التعليم المغربي',
  description: 'معلومات حول مباريات ولوج المدارس والمعاهد العليا.',
};

export default async function ConcoursPage() {
  const breadcrumbs = [
    { name: 'الرئيسية', url: '/' },
    { name: 'المباريات', url: '/concours' },
  ];

  const concours = await getCollection('concours');

  return (
    <>
      <Breadcrumbs items={breadcrumbs} />
      <div className="container mx-auto px-4 py-8 fade-in">
        <div className="text-center">
            <School className="h-24 w-24 text-primary mx-auto mb-6" />
            <h1 className="text-4xl font-bold">المباريات</h1>
            <p className="text-lg text-muted-foreground mt-2">
            معلومات وتواريخ مباريات ولوج المدارس والمعاهد العليا.
            </p>
        </div>

        <div className="mt-12 grid gap-8">
            {concours.length > 0 ? (
                concours.map((c: any) => (
                    <Card key={c.id} className="hover-scale">
                        <CardContent className="p-6">
                            <div className="flex flex-col md:flex-row justify-between md:items-center">
                                <div className="flex-grow">
                                    <h3 className="text-xl font-bold text-primary mb-2">{c.title}</h3>
                                    <p className="text-muted-foreground mb-4">{c.description}</p>
                                </div>
                                <div className="flex-shrink-0 mt-4 md:mt-0">
                                    <Button asChild>
                                        <Link href="#">
                                            عرض التفاصيل
                                            <ChevronLeft className="ms-2 h-4 w-4"/>
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                            <div className="border-t mt-4 pt-4 flex flex-wrap items-center gap-x-6 gap-y-2 text-sm text-muted-foreground">
                                <div className="flex items-center gap-2"><Building className="h-4 w-4"/> {c.organizer}</div>
                                <div className="flex items-center gap-2"><Calendar className="h-4 w-4"/> آخر أجل: {c.deadline}</div>
                            </div>
                        </CardContent>
                    </Card>
                ))
            ) : (
                <div className="text-center py-16">
                    <Card className="max-w-md mx-auto">
                        <CardContent className="p-10">
                            <h2 className="text-2xl font-semibold">لا توجد مباريات حالياً</h2>
                            <p className="text-muted-foreground mt-2">
                                نعمل حالياً على تجميع وتوفير جميع المعلومات المتعلقة بالمباريات. يرجى العودة قريباً.
                            </p>
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
      </div>
    </>
  );
}
