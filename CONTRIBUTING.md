# دليل المساهمة - Contributing Guide

شكراً لاهتمامك بالمساهمة في مشروع التعليم المغربي! نرحب بجميع أنواع المساهمات.

## 🤝 أنواع المساهمات

### 📝 المحتوى التعليمي
- إضافة دروس جديدة
- تحسين الدروس الموجودة
- إضافة فروض وامتحانات
- ترجمة المحتوى

### 💻 التطوير التقني
- إصلاح الأخطاء (Bug fixes)
- إضافة ميزات جديدة
- تحسين الأداء
- تحسين إمكانية الوصول

### 🎨 التصميم
- تحسين واجهة المستخدم
- إضافة أيقونات ورسوم
- تحسين تجربة المستخدم

### 📚 الوثائق
- تحسين التوثيق
- إضافة أمثلة
- ترجمة الوثائق

## 🚀 البدء

### 1. إعداد البيئة المحلية
```bash
# استنساخ المشروع
git clone https://github.com/Ridanotx/MaghrebLearn.git
cd MaghrebLearn

# تثبيت التبعيات
npm install

# نسخ ملف البيئة
cp .env.example .env.local

# تشغيل المشروع
npm run dev
```

### 2. إنشاء فرع جديد
```bash
git checkout -b feature/اسم-الميزة
# أو
git checkout -b fix/اسم-الإصلاح
```

## 📋 معايير الكود

### TypeScript
- استخدم TypeScript في جميع الملفات
- أضف أنواع البيانات للمتغيرات والدوال
- تجنب استخدام `any` إلا عند الضرورة

```typescript
// ✅ جيد
interface User {
  id: string;
  name: string;
  email: string;
}

// ❌ تجنب
const user: any = { ... };
```

### React Components
- استخدم Functional Components مع Hooks
- أضف PropTypes أو TypeScript interfaces
- استخدم أسماء وصفية للمكونات

```tsx
// ✅ جيد
interface ButtonProps {
  children: React.ReactNode;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

export function Button({ children, onClick, variant = 'primary' }: ButtonProps) {
  return (
    <button 
      className={`btn btn-${variant}`}
      onClick={onClick}
    >
      {children}
    </button>
  );
}
```

### CSS/Tailwind
- استخدم Tailwind CSS للتصميم
- اتبع نظام التصميم الموحد
- تأكد من الاستجابة للأجهزة المختلفة

```tsx
// ✅ جيد
<div className="flex flex-col md:flex-row gap-4 p-6">
  <Card className="flex-1 hover:shadow-lg transition-shadow">
    {/* المحتوى */}
  </Card>
</div>
```

### إمكانية الوصول
- أضف `alt` للصور
- استخدم `aria-label` للعناصر التفاعلية
- تأكد من إمكانية التنقل بلوحة المفاتيح

```tsx
// ✅ جيد
<button 
  aria-label="إغلاق النافذة"
  onClick={handleClose}
>
  <X className="h-4 w-4" aria-hidden="true" />
</button>
```

## 🧪 الاختبار

### قبل إرسال Pull Request
```bash
# فحص TypeScript
npm run typecheck

# فحص ESLint
npm run lint

# تنسيق الكود
npm run format

# أو تشغيل جميع الفحوصات
npm run check
```

### اختبار الميزات
- اختبر الميزة على أجهزة مختلفة
- تأكد من عمل الميزة مع قارئات الشاشة
- اختبر مع بيانات مختلفة

## 📝 رسائل Commit

استخدم رسائل commit واضحة ووصفية:

```bash
# ✅ جيد
git commit -m "feat: إضافة صفحة البحث في الدروس"
git commit -m "fix: إصلاح مشكلة التنقل في الهاتف"
git commit -m "docs: تحديث دليل التثبيت"

# ❌ تجنب
git commit -m "تحديث"
git commit -m "إصلاح"
```

### أنواع Commit
- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث الوثائق
- `style`: تغييرات التصميم
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة اختبارات
- `chore`: مهام صيانة

## 🔄 عملية Pull Request

### 1. قبل الإرسال
- [ ] تأكد من عمل الكود محلياً
- [ ] اتبع معايير الكود
- [ ] أضف وثائق للميزات الجديدة
- [ ] اختبر على أجهزة مختلفة

### 2. وصف Pull Request
```markdown
## الوصف
وصف مختصر للتغييرات المقترحة

## نوع التغيير
- [ ] إصلاح خطأ
- [ ] ميزة جديدة
- [ ] تحسين الأداء
- [ ] تحديث الوثائق

## الاختبار
- [ ] تم اختبار الكود محلياً
- [ ] تم اختبار إمكانية الوصول
- [ ] تم اختبار الاستجابة

## لقطات الشاشة (إن وجدت)
أضف لقطات شاشة للتغييرات المرئية
```

### 3. مراجعة الكود
- سيتم مراجعة جميع Pull Requests
- قد نطلب تعديلات قبل الدمج
- نقدر صبرك أثناء المراجعة

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ
- ابحث في Issues الموجودة
- تأكد من أن الخطأ قابل للإعادة
- جمع معلومات النظام

### معلومات مطلوبة
- وصف الخطأ
- خطوات إعادة الإنتاج
- السلوك المتوقع
- السلوك الفعلي
- لقطات شاشة (إن أمكن)
- معلومات النظام (المتصفح، نظام التشغيل)

## 💡 اقتراح الميزات

### قبل الاقتراح
- ابحث في Issues الموجودة
- تأكد من أن الميزة تناسب أهداف المشروع
- فكر في التأثير على المستخدمين

### معلومات مطلوبة
- وصف الميزة المقترحة
- المشكلة التي تحلها
- البدائل المدروسة
- أمثلة للاستخدام

## 📞 التواصل

- **GitHub Issues**: للأخطاء والاقتراحات
- **Email**: <EMAIL> للاستفسارات العامة
- **Discussions**: للنقاشات والأسئلة

## 🏆 الاعتراف بالمساهمين

نقدر جميع المساهمات ونعترف بها في:
- ملف README
- صفحة المساهمين
- ملاحظات الإصدار

---

**شكراً لك على مساهمتك في تحسين التعليم المغربي! 🇲🇦**
