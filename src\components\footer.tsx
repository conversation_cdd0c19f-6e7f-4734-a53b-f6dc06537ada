import Link from 'next/link';
import { BookOpen, Facebook, Mail, Youtube } from 'lucide-react';

const footerLinks = {
  levels: [
    { href: '/lessons/primary', label: 'التعليم الابتدائي' },
    { href: '/lessons/college', label: 'التعليم الإعدادي' },
    { href: '/lessons/lycee', label: 'التعليم الثانوي' },
  ],
  sections: [
    { href: '/lessons', label: 'الدروس' },
    { href: '/devoirs', label: 'الفروض' },
    { href: '/exams', label: 'الامتحانات' },
    { href: '/docs', label: 'الوثائق' },
  ],
  useful: [
    { href: '/orientation', label: 'التوجيه المدرسي' },
    { href: '/concours', label: 'المباريات' },
    { href: '/tests', label: 'اختبارات تفاعلية' },
    { href: '/news', label: 'الأخبار' },
  ],
};

export function Footer() {
  return (
    <footer className="bg-gray-800 text-white mt-16">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center gap-2 mb-4">
              <BookOpen className="h-8 w-8 text-primary" />
              <h3 className="text-xl font-bold">التعليم المغربي</h3>
            </div>
            <p className="text-gray-300 mb-4">
              موقع تعليمي مغربي شامل يوفر محتوى مجاني لجميع المستويات الدراسية
            </p>
            <div className="flex gap-4">
              <Link href="#" aria-label="Facebook">
                <Facebook className="h-6 w-6 cursor-pointer hover:text-blue-400" />
              </Link>
              <Link href="#" aria-label="Youtube">
                <Youtube className="h-6 w-6 cursor-pointer hover:text-red-500" />
              </Link>
              <Link href="#" aria-label="Email">
                <Mail className="h-6 w-6 cursor-pointer hover:text-gray-400" />
              </Link>
            </div>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-lg">المستويات الدراسية</h4>
            <ul className="space-y-2 text-gray-300">
              {footerLinks.levels.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="hover:text-white transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-lg">الأقسام</h4>
            <ul className="space-y-2 text-gray-300">
              {footerLinks.sections.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="hover:text-white transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-4 text-lg">روابط مفيدة</h4>
            <ul className="space-y-2 text-gray-300">
              {footerLinks.useful.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="hover:text-white transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 موقع التعليم المغربي. جميع الحقوق محفوظة.</p>
          <p className="mt-2 text-sm">
            محتوى مجاني للجميع • مع تحيات فريق التعليم المغربي 🇲🇦
          </p>
        </div>
      </div>
    </footer>
  );
}
