
import { getNewsArticle } from '@/lib/data';
import { notFound } from 'next/navigation';
import { Breadcrumbs } from '@/components/breadcrumbs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, Eye, Tag } from 'lucide-react';
import type { Metadata } from 'next';

type Props = {
  params: { slug: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const article: any = await getNewsArticle(params.slug);

  if (!article) {
    return {
      title: 'Article Not Found',
    };
  }

  return {
    title: `${article.title} | التعليم المغربي`,
    description: article.excerpt,
  };
}

export default async function NewsArticlePage({ params }: Props) {
  const article: any = await getNewsArticle(params.slug);

  if (!article) {
    notFound();
  }

  const breadcrumbs = [
    { name: 'الرئيسية', url: '/' },
    { name: 'الأخبار', url: '/news' },
    { name: article.title, url: `/news/${article.slug}` },
  ];

  return (
    <>
      <Breadcrumbs items={breadcrumbs} />
      <div className="container mx-auto px-4 py-8 fade-in">
        <Card>
            <CardHeader>
                <div className="text-6xl mb-4">{article.image}</div>
                <CardTitle className="text-4xl font-bold">{article.title}</CardTitle>
                <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-muted-foreground pt-4">
                    <div className="flex items-center gap-2"><CalendarDays className="h-4 w-4" /> {article.date}</div>
                    <div className="flex items-center gap-2"><Tag className="h-4 w-4" /> <Badge variant="secondary">{article.category}</Badge></div>
                    <div className="flex items-center gap-2"><Eye className="h-4 w-4" /> {article.views} مشاهدة</div>
                </div>
            </CardHeader>
          <CardContent>
            <article className="prose max-w-none dark:prose-invert">
              <p className="lead">{article.excerpt}</p>
              <p>{article.content}</p>
            </article>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
