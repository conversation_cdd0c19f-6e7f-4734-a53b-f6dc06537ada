
// This file is the "database" for the application.
// In a real-world scenario, this data would come from a database or a CMS.
import { db } from './firebase';
import { collection, getDocs, doc, getDoc, query, where } from 'firebase/firestore';

export const siteData = {
  cycles: {
    primary: {
      name: 'التعليم الابتدائي',
      icon: 'Backpack',
      levels: {
        '1ap': {
          name: 'السنة الأولى ابتدائي',
          subjects: ['arabic', 'math', 'french', 'islamic', 'activity'],
        },
        '2ap': {
          name: 'السنة الثانية ابتدائي',
          subjects: ['arabic', 'math', 'french', 'islamic', 'activity'],
        },
        '3ap': {
          name: 'السنة الثالثة ابتدائي',
          subjects: [
            'arabic',
            'math',
            'french',
            'islamic',
            'activity',
            'amazigh',
          ],
        },
        '4ap': {
          name: 'السنة الرابعة ابتدائي',
          subjects: [
            'arabic',
            'math',
            'french',
            'islamic',
            'activity',
            'amazigh',
          ],
        },
        '5ap': {
          name: 'السنة الخامسة ابتدائي',
          subjects: [
            'arabic',
            'math',
            'french',
            'islamic',
            'activity',
            'amazigh',
          ],
        },
        '6ap': {
          name: 'السنة السادسة ابتدائي',
          subjects: [
            'arabic',
            'math',
            'french',
            'islamic',
            'activity',
            'amazigh',
          ],
        },
      },
    },
    college: {
      name: 'التعليم الإعدادي',
      icon: 'GraduationCap',
      levels: {
        '1ac': {
          name: 'السنة الأولى إعدادي',
          subjects: [
            'arabic',
            'math',
            'french',
            'english',
            'islamic',
            'history',
            'geography',
            'physics',
            'svt',
            'tech',
            'art',
            'sport',
          ],
        },
        '2ac': {
          name: 'السنة الثانية إعدادي',
          subjects: [
            'arabic',
            'math',
            'french',
            'english',
            'islamic',
            'history',
            'geography',
            'physics',
            'svt',
            'tech',
            'art',
            'sport',
          ],
        },
        '3ac': {
          name: 'السنة الثالثة إعدادي',
          subjects: [
            'arabic',
            'math',
            'french',
            'english',
            'islamic',
            'history',
            'geography',
            'physics',
            'svt',
            'tech',
            'art',
            'sport',
          ],
        },
      },
    },
    lycee: {
      name: 'التعليم الثانوي',
      icon: 'Library',
      levels: {
        tc: {
          name: 'الجذع المشترك',
          branches: {
            'tc-science': {
              name: 'جذع مشترك علمي',
              subjects: [
                'arabic',
                'math',
                'french',
                'english',
                'islamic',
                'history',
                'geography',
                'physics',
                'svt',
                'philosophy',
              ],
            },
            'tc-lettres': {
              name: 'جذع مشترك آداب وعلوم إنسانية',
              subjects: [
                'arabic',
                'math',
                'french',
                'english',
                'islamic',
                'history',
                'geography',
                'philosophy',
              ],
            },
          },
        },
        '1bac': {
          name: 'الأولى بكالوريا',
          branches: {
            '1bac-science-exp': {
              name: 'علوم تجريبية',
              subjects: [
                'arabic',
                'math',
                'french',
                'english',
                'islamic',
                'history',
                'geography',
                'physics',
                'svt',
                'philosophy',
              ],
            },
            '1bac-science-math': {
              name: 'علوم رياضية',
              subjects: [
                'arabic',
                'math',
                'french',
                'english',
                'islamic',
                'physics',
                'svt',
                'philosophy',
              ],
            },
            '1bac-lettres': {
              name: 'آداب وعلوم إnsانية',
              subjects: [
                'arabic',
                'math',
                'french',
                'english',
                'islamic',
                'history',
                'geography',
                'philosophy',
              ],
            },
            '1bac-eco': {
              name: 'علوم اقتصادية وتدبير',
              subjects: [
                'arabic',
                'math',
                'french',
                'english',
                'islamic',
                'history',
                'geography',
                'economics',
                'philosophy',
              ],
            },
          },
        },
        '2bac': {
          name: 'الثانية بكالوريا',
          branches: {
            '2bac-pc': {
              name: 'علوم فيزيائية',
              subjects: [
                'math',
                'english',
                'physics',
                'svt',
                'philosophy',
              ],
            },
            '2bac-svt': {
                name: 'علوم الحياة والأرض',
                subjects: [
                  'math',
                  'english',
                  'physics',
                  'svt',
                  'philosophy',
                ],
            },
            '2bac-science-math-a': {
              name: 'علوم رياضية أ',
              subjects: [
                'math',
                'english',
                'physics',
                'philosophy',
              ],
            },
            '2bac-science-math-b': {
                name: 'علوم رياضية ب',
                subjects: [
                  'math',
                  'english',
                  'physics',
                  'svt',
                  'philosophy',
                ],
            },
            '2bac-lettres': {
              name: 'آداب',
              subjects: [
                'arabic',
                'french',
                'english',
                'history',
                'geography',
                'philosophy',
              ],
            },
            '2bac-humaines': {
                name: 'علوم إنسانية',
                subjects: [
                  'arabic',
                  'french',
                  'english',
                  'history',
                  'geography',
                  'philosophy',
                ],
            },
            '2bac-eco': {
              name: 'علوم اقتصادية',
              subjects: [
                'math',
                'english',
                'economics',
                'philosophy',
              ],
            },
          },
        },
      },
    },
  },
  subjects: {
    arabic: { name: 'اللغة العربية', icon: 'PenSquare', language: 'ar' },
    math: { name: 'الرياضيات', icon: 'Calculator', language: 'fr' },
    french: { name: 'اللغة الفرنسية', icon: 'Languages', language: 'fr' },
    english: { name: 'اللغة الإنجليزية', icon: 'Languages', language: 'en' },
    spanish: { name: 'اللغة الإسبانية', icon: 'Languages', language: 'es' },
    islamic: { name: 'التربية الإسلامية', icon: 'BookMarked', language: 'ar' },
    history: { name: 'التاريخ', icon: 'Landmark', language: 'ar' },
    geography: { name: 'الجغرافيا', icon: 'Globe', language: 'ar' },
    physics: { name: 'الفيزياء والكيمياء', icon: 'Atom', language: 'fr' },
    svt: { name: 'علوم الحياة والأرض', icon: 'Leaf', language: 'fr' },
    philosophy: { name: 'الفلسفة', icon: 'BrainCircuit', language: 'ar' },
    economics: { name: 'الاقتصاد والتدبير', icon: 'Briefcase', language: 'ar' },
    tech: { name: 'التكنولوجيا', icon: 'Cpu', language: 'fr' },
    art: { name: 'التربية الفنية', icon: 'Palette', language: 'ar' },
    sport: { name: 'التربية البدنية', icon: 'Goal', language: 'ar' },
    activity: { name: 'النشاط العلمي', icon: 'TestTube', language: 'ar' },
    amazigh: { name: 'الأمازيغية', icon: 'Languages', language: 'ar' },
  },
  sections: {
    lessons: { name: 'الدروس', icon: 'BookOpen' },
    devoirs: { name: 'الفروض', icon: 'FileText' },
    exams: { name: 'الامتحانات', icon: 'FileText' },
    docs: { name: 'الوثائق', icon: 'FileText' },
    orientation: { name: 'التوجيه', icon: 'GraduationCap' },
    concours: { name: 'المباريات', icon: 'Trophy' },
    tests: { name: 'اختبارات تفاعلية', icon: 'FlaskConical' },
    news: { name: 'الأخبار', icon: 'Newspaper' },
  },
};

// Firestore data fetching functions

interface NewsArticle {
    id: string;
    date: string;
    title: string;
    excerpt: string;
    content?: string;
    category?: string;
    image?: string;
    views?: number;
    slug: string;
}

export async function getNewsArticles(): Promise<NewsArticle[]> {
    try {
        const newsCol = collection(db, 'news');
        const newsSnapshot = await getDocs(newsCol);
        const newsList = newsSnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
        })) as NewsArticle[];

        return newsList.sort((a, b) =>
            new Date(b.date).getTime() - new Date(a.date).getTime()
        );
    } catch (error) {
        console.error('Error fetching news articles:', error);
        return [];
    }
}

export async function getNewsArticle(slug: string): Promise<NewsArticle | null> {
    try {
        const docRef = doc(db, 'news', slug);
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
            return { id: docSnap.id, ...docSnap.data() } as NewsArticle;
        } else {
            return null;
        }
    } catch (error) {
        console.error('Error fetching news article:', error);
        return null;
    }
}

interface ContentItem {
    id: string;
    title: string;
    description?: string;
    content?: string;
    slug: string;
    downloads?: number;
    year?: string;
    session?: string;
    chapters?: number;
    questions?: Array<{ question: string; answer: string }>;
}

// A generic function to fetch a list of content items from a subcollection
export async function getContentList(section: string, path: string): Promise<ContentItem[]> {
  try {
    const pathSegments = path.split('/').filter(Boolean);
    if (pathSegments.length === 0) return [];

    // The path should be like: section/docId/subcollection/docId/.../subcollection
    // The path passed in is missing the section, so we add it.
    // e.g. path = 'lycee/tc/tc-science/math' and section = 'lessons'
    // The full path will be 'lessons/lycee/tc/tc-science/math/items'
    // This is valid since it has 5 segments.
    const fullPath = [section, ...pathSegments, 'items'].join('/');

    const contentRef = collection(db, fullPath);
    const contentSnapshot = await getDocs(contentRef);
    const contentList = contentSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
    })) as ContentItem[];
    return contentList;
  } catch(error) {
    console.error("Failed to fetch content list for", section, path, error);
    return [];
  }
}

// A generic function to fetch a single content item
export async function getContentItem(section: string, path: string, slug: string): Promise<ContentItem | null> {
  try {
    const pathSegments = path.split('/').filter(Boolean);
    if (pathSegments.length === 0) return null;

    const fullPath = [section, ...pathSegments, 'items'].join('/');

    const itemsRef = collection(db, fullPath);
    const q = query(itemsRef, where("slug", "==", slug));
    const querySnapshot = await getDocs(q);
    if (!querySnapshot.empty) {
      const docSnap = querySnapshot.docs[0];
      return { id: docSnap.id, ...docSnap.data() } as ContentItem;
    } else {
      return null;
    }
  } catch(error) {
    console.error("Failed to fetch content item for", section, path, slug, error);
    return null;
  }
}


// Generic function to fetch all items from a top-level collection
export async function getCollection(collectionName: string): Promise<any[]> {
    try {
        const col = collection(db, collectionName);
        const snapshot = await getDocs(col);
        const list = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
        return list;
    } catch(error) {
        console.error(`Failed to fetch collection ${collectionName}`, error);
        return [];
    }
}
