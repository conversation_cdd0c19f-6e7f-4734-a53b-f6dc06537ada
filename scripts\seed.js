
// This script is used to seed the database with initial data.
// To run it, you would typically use a command like: node scripts/seed.js
// Note: This is a simplified script. In a real-world scenario, you would need
// to handle authentication and error handling more robustly.

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, doc, setDoc } = require('firebase/firestore');
require('dotenv').config({ path: '.env.local' });

// Firebase configuration using environment variables
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

const newsData = [
    {
      id: "exam-dates-2024",
      slug: 'exam-dates-2024',
      title: 'تواريخ الامتحانات الوطنية 2024',
      excerpt:
        'وزارة التربية الوطنية تعلن عن التواريخ الرسمية للامتحانات الوطنية للبكالوريا 2024.',
      content:
        'أعلنت وزارة التربية الوطنية والتعليم الأولي والرياضة عن التواريخ الرسمية لإجراء الامتحانات الوطنية للبكالوريا برسم سنة 2024. ستبدأ الامتحانات في شهر يونيو وتستمر لمدة أسبوع. وقد تم نشر الجدول الزمني المفصل على الموقع الرسمي للوزارة. ننصح جميع التلاميذ بالاطلاع على الجدول والاستعداد الجيد.',
      date: '2024-01-15',
      category: 'امتحانات',
      image: '📅',
      views: 5200,
    },
    {
      id: "curriculum-updates-2024",
      slug: 'curriculum-updates-2024',
      title: 'مستجدات المناهج الدراسية للموسم الجديد',
      excerpt:
        'تحديثات مهمة على المناهج الدراسية لجميع المستويات التعليمية.',
      content:
        'في إطار تطوير المنظومة التعليمية، تم إدخال تحديثات مهمة على المناهج الدراسية للموسم الدراسي الجديد. تشمل هذه التحديثات مواد جديدة وتعديلات على المقررات الحالية بهدف مواكبة التطورات العلمية والتكنولوجية.',
      date: '2024-01-10',
      category: 'مناهج',
      image: '📚',
      views: 3800,
    },
    {
      id: "exam-preparation-tips",
      slug: 'exam-preparation-tips',
      title: 'نصائح للتحضير الجيد للامتحانات',
      excerpt: 'دليل شامل لمساعدة التلاميذ على التحضير الأمثل للامتحانات.',
      content:
        'مع اقتراب موسم الامتحانات، نقدم لكم مجموعة من النصائح المهمة للتحضير الجيد. من الضروري تنظيم الوقت، ومراجعة الدروس بانتظام، وحل التمارين والامتحانات السابقة. كما ننصح بالحصول على قسط كاف من النوم والتغذية السليمة.',
      date: '2024-01-05',
      category: 'نصائح',
      image: '💡',
      views: 4500,
    },
    {
      id: "new-digital-platform",
      slug: 'new-digital-platform',
      title: 'إطلاق منصة رقمية جديدة للتعليم عن بعد',
      excerpt:
        'وزارة التربية الوطنية تطلق منصة رقمية متطورة لدعم التعليم عن بعد.',
      content:
        'في خطوة نحو تحديث المنظومة التعليمية، أطلقت الوزارة منصة رقمية جديدة تهدف إلى دعم التعليم عن بعد. توفر المنصة دروساً تفاعلية، وتمارين، ومنتديات للنقاش بين التلاميذ والأساتذة.',
      date: '2024-01-01',
      category: 'تكنولوجيا',
      image: '💻',
      views: 2900,
    },
];

const concoursData = [
  {
      id: 'medecine-2024',
      title: 'مباراة ولوج كليات الطب والصيدلة وطب الأسنان 2024',
      description: 'مباراة مشتركة لولوج السنة الأولى في كليات الطب والصيدلة وكليات طب الأسنان.',
      organizer: 'وزارة التعليم العالي',
      deadline: '2024-07-30'
  },
  {
      id: 'ensa-2024',
      title: 'مباراة ولوج المدارس الوطنية للعلوم التطبيقية (ENSA) 2024',
      description: 'تفتح هذه المباراة أبواب أفضل مدارس الهندسة في المغرب.',
      organizer: 'شبكة ENSA',
      deadline: '2024-07-25'
  },
  {
      id: 'apesa-2024',
      title: 'مباراة ولوج معهد الحسن الثاني للزراعة والبيطرة (APESA) 2024',
      description: 'مباراة ولوج السنة التحضيرية للدراسات العليا الفلاحية.',
      organizer: 'معهد الحسن الثاني للزراعة والبيطرة',
      deadline: '2024-07-20'
  }
];

const orientationDocsData = [
  {
      id: 'guide-post-bac-2024',
      title: 'دليل التوجيه لما بعد البكالوريا 2024',
      description: 'دليل شامل حول الآفاق الدراسية والمهنية بعد الحصول على شهادة البكالوريا.',
      downloads: '12,500',
      pages: 150
  },
  {
      id: 'guide-ofppt-2024',
      title: 'دليل شعب التكوين المهني (OFPPT) 2024',
      description: 'يقدم هذا الدليل نظرة شاملة عن جميع شعب ومستويات التكوين المهني المتاحة.',
      downloads: '8,700',
      pages: 95
  }
];

const testsData = [
  {
      id: 'math-final-prep',
      title: 'اختبار تجريبي في الرياضيات - استعداد للوطني',
      subject: 'math',
      questions: 20,
      duration: 120,
      attempts: '1,200'
  },
  {
      id: 'physics-waves-quiz',
      title: 'اختبار في درس الموجات - فيزياء',
      subject: 'physics',
      questions: 15,
      duration: 45,
      attempts: '2,300'
  },
  {
      id: 'philosophy-concepts-quiz',
      title: 'اختبار في المفاهيم الفلسفية',
      subject: 'philosophy',
      questions: 25,
      duration: 30,
      attempts: '950'
  }
];

async function seedCollection(db, collectionName, data) {
    const collectionRef = collection(db, collectionName);
    console.log(`\nSeeding '${collectionName}' collection...`);

    for (const item of data) {
      const docRef = doc(collectionRef, item.id);
      await setDoc(docRef, item);
      console.log(`  Added: ${item.title}`);
    }
}


async function seedDatabase() {
  try {
    console.log('Initializing Firebase...');
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);
    console.log('Firebase initialized.');

    await seedCollection(db, 'news', newsData);
    await seedCollection(db, 'concours', concoursData);
    await seedCollection(db, 'orientation', orientationDocsData);
    await seedCollection(db, 'tests', testsData);

    console.log('\nDatabase seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

seedDatabase();
